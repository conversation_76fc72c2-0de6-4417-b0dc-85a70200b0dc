﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ProcureToPay.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class FixedSnakeCaseSchema : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_goods_receipt_note_lines_delivery_note_lines_DeliveryNoteLi~",
                table: "goods_receipt_note_lines");

            migrationBuilder.DropForeignKey(
                name: "FK_return_authorization_lines_sales_order_lines_OriginalSalesO~",
                table: "return_authorization_lines");

            migrationBuilder.DropForeignKey(
                name: "FK_sales_order_lines_projects_ProjectId1",
                table: "sales_order_lines");

            migrationBuilder.DropForeignKey(
                name: "FK_sales_order_lines_sales_order_lines_ParentSalesOrderLineSal~",
                table: "sales_order_lines");

            migrationBuilder.DropForeignKey(
                name: "FK_sales_orders_sales_territories_SalesTerritoryId1",
                schema: "public",
                table: "sales_orders");

            migrationBuilder.DropForeignKey(
                name: "FK_submittal_reviews_technical_submittals_TechnicalSubmittalId1",
                table: "submittal_reviews");

            migrationBuilder.RenameColumn(
                name: "TechnicalSubmittalId1",
                table: "submittal_reviews",
                newName: "technical_submittal_id1");

            migrationBuilder.RenameIndex(
                name: "IX_submittal_reviews_TechnicalSubmittalId1",
                table: "submittal_reviews",
                newName: "IX_submittal_reviews_technical_submittal_id1");

            migrationBuilder.RenameColumn(
                name: "SalesTerritoryId1",
                schema: "public",
                table: "sales_orders",
                newName: "sales_territory_id1");

            migrationBuilder.RenameIndex(
                name: "IX_sales_orders_SalesTerritoryId1",
                schema: "public",
                table: "sales_orders",
                newName: "IX_sales_orders_sales_territory_id1");

            migrationBuilder.RenameColumn(
                name: "ProjectId1",
                table: "sales_order_lines",
                newName: "project_id1");

            migrationBuilder.RenameColumn(
                name: "ParentSalesOrderLineSalesOrderId",
                table: "sales_order_lines",
                newName: "parent_sales_order_line_sales_order_id");

            migrationBuilder.RenameColumn(
                name: "ParentSalesOrderLineLineNumber",
                table: "sales_order_lines",
                newName: "parent_sales_order_line_line_number");

            migrationBuilder.RenameIndex(
                name: "IX_sales_order_lines_ProjectId1",
                table: "sales_order_lines",
                newName: "IX_sales_order_lines_project_id1");

            migrationBuilder.RenameIndex(
                name: "IX_sales_order_lines_ParentSalesOrderLineSalesOrderId_ParentSa~",
                table: "sales_order_lines",
                newName: "IX_sales_order_lines_parent_sales_order_line_sales_order_id_pa~");

            migrationBuilder.RenameColumn(
                name: "OriginalSalesOrderLineSalesOrderId",
                table: "return_authorization_lines",
                newName: "original_sales_order_line_sales_order_id");

            migrationBuilder.RenameColumn(
                name: "OriginalSalesOrderLineLineNumber",
                table: "return_authorization_lines",
                newName: "original_sales_order_line_line_number");

            migrationBuilder.RenameIndex(
                name: "IX_return_authorization_lines_OriginalSalesOrderLineSalesOrder~",
                table: "return_authorization_lines",
                newName: "IX_return_authorization_lines_original_sales_order_line_sales_~");

            migrationBuilder.RenameColumn(
                name: "DeliveryNoteLineLineNumber",
                table: "goods_receipt_note_lines",
                newName: "delivery_note_line_line_number");

            migrationBuilder.RenameColumn(
                name: "DeliveryNoteLineDeliveryNoteId",
                table: "goods_receipt_note_lines",
                newName: "delivery_note_line_delivery_note_id");

            migrationBuilder.RenameIndex(
                name: "IX_goods_receipt_note_lines_DeliveryNoteLineDeliveryNoteId_Del~",
                table: "goods_receipt_note_lines",
                newName: "IX_goods_receipt_note_lines_delivery_note_line_delivery_note_i~");

            migrationBuilder.RenameIndex(
                name: "user_name_index",
                table: "asp_net_users",
                newName: "ix_asp_net_users_normalized_user_name");

            migrationBuilder.RenameIndex(
                name: "email_index",
                table: "asp_net_users",
                newName: "ix_asp_net_users_normalized_email");

            migrationBuilder.RenameIndex(
                name: "role_name_index",
                table: "asp_net_roles",
                newName: "ix_asp_net_roles_normalized_name");

            migrationBuilder.AddForeignKey(
                name: "FK_goods_receipt_note_lines_delivery_note_lines_delivery_note_~",
                table: "goods_receipt_note_lines",
                columns: new[] { "delivery_note_line_delivery_note_id", "delivery_note_line_line_number" },
                principalTable: "delivery_note_lines",
                principalColumns: new[] { "delivery_note_id", "line_number" });

            migrationBuilder.AddForeignKey(
                name: "FK_return_authorization_lines_sales_order_lines_original_sales~",
                table: "return_authorization_lines",
                columns: new[] { "original_sales_order_line_sales_order_id", "original_sales_order_line_line_number" },
                principalTable: "sales_order_lines",
                principalColumns: new[] { "sales_order_id", "line_number" });

            migrationBuilder.AddForeignKey(
                name: "FK_sales_order_lines_projects_project_id1",
                table: "sales_order_lines",
                column: "project_id1",
                principalSchema: "public",
                principalTable: "projects",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "FK_sales_order_lines_sales_order_lines_parent_sales_order_line~",
                table: "sales_order_lines",
                columns: new[] { "parent_sales_order_line_sales_order_id", "parent_sales_order_line_line_number" },
                principalTable: "sales_order_lines",
                principalColumns: new[] { "sales_order_id", "line_number" });

            migrationBuilder.AddForeignKey(
                name: "FK_sales_orders_sales_territories_sales_territory_id1",
                schema: "public",
                table: "sales_orders",
                column: "sales_territory_id1",
                principalTable: "sales_territories",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "FK_submittal_reviews_technical_submittals_technical_submittal~1",
                table: "submittal_reviews",
                column: "technical_submittal_id1",
                principalTable: "technical_submittals",
                principalColumn: "id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_goods_receipt_note_lines_delivery_note_lines_delivery_note_~",
                table: "goods_receipt_note_lines");

            migrationBuilder.DropForeignKey(
                name: "FK_return_authorization_lines_sales_order_lines_original_sales~",
                table: "return_authorization_lines");

            migrationBuilder.DropForeignKey(
                name: "FK_sales_order_lines_projects_project_id1",
                table: "sales_order_lines");

            migrationBuilder.DropForeignKey(
                name: "FK_sales_order_lines_sales_order_lines_parent_sales_order_line~",
                table: "sales_order_lines");

            migrationBuilder.DropForeignKey(
                name: "FK_sales_orders_sales_territories_sales_territory_id1",
                schema: "public",
                table: "sales_orders");

            migrationBuilder.DropForeignKey(
                name: "FK_submittal_reviews_technical_submittals_technical_submittal~1",
                table: "submittal_reviews");

            migrationBuilder.RenameColumn(
                name: "technical_submittal_id1",
                table: "submittal_reviews",
                newName: "TechnicalSubmittalId1");

            migrationBuilder.RenameIndex(
                name: "IX_submittal_reviews_technical_submittal_id1",
                table: "submittal_reviews",
                newName: "IX_submittal_reviews_TechnicalSubmittalId1");

            migrationBuilder.RenameColumn(
                name: "sales_territory_id1",
                schema: "public",
                table: "sales_orders",
                newName: "SalesTerritoryId1");

            migrationBuilder.RenameIndex(
                name: "IX_sales_orders_sales_territory_id1",
                schema: "public",
                table: "sales_orders",
                newName: "IX_sales_orders_SalesTerritoryId1");

            migrationBuilder.RenameColumn(
                name: "project_id1",
                table: "sales_order_lines",
                newName: "ProjectId1");

            migrationBuilder.RenameColumn(
                name: "parent_sales_order_line_sales_order_id",
                table: "sales_order_lines",
                newName: "ParentSalesOrderLineSalesOrderId");

            migrationBuilder.RenameColumn(
                name: "parent_sales_order_line_line_number",
                table: "sales_order_lines",
                newName: "ParentSalesOrderLineLineNumber");

            migrationBuilder.RenameIndex(
                name: "IX_sales_order_lines_project_id1",
                table: "sales_order_lines",
                newName: "IX_sales_order_lines_ProjectId1");

            migrationBuilder.RenameIndex(
                name: "IX_sales_order_lines_parent_sales_order_line_sales_order_id_pa~",
                table: "sales_order_lines",
                newName: "IX_sales_order_lines_ParentSalesOrderLineSalesOrderId_ParentSa~");

            migrationBuilder.RenameColumn(
                name: "original_sales_order_line_sales_order_id",
                table: "return_authorization_lines",
                newName: "OriginalSalesOrderLineSalesOrderId");

            migrationBuilder.RenameColumn(
                name: "original_sales_order_line_line_number",
                table: "return_authorization_lines",
                newName: "OriginalSalesOrderLineLineNumber");

            migrationBuilder.RenameIndex(
                name: "IX_return_authorization_lines_original_sales_order_line_sales_~",
                table: "return_authorization_lines",
                newName: "IX_return_authorization_lines_OriginalSalesOrderLineSalesOrder~");

            migrationBuilder.RenameColumn(
                name: "delivery_note_line_line_number",
                table: "goods_receipt_note_lines",
                newName: "DeliveryNoteLineLineNumber");

            migrationBuilder.RenameColumn(
                name: "delivery_note_line_delivery_note_id",
                table: "goods_receipt_note_lines",
                newName: "DeliveryNoteLineDeliveryNoteId");

            migrationBuilder.RenameIndex(
                name: "IX_goods_receipt_note_lines_delivery_note_line_delivery_note_i~",
                table: "goods_receipt_note_lines",
                newName: "IX_goods_receipt_note_lines_DeliveryNoteLineDeliveryNoteId_Del~");

            migrationBuilder.RenameIndex(
                name: "ix_asp_net_users_normalized_user_name",
                table: "asp_net_users",
                newName: "user_name_index");

            migrationBuilder.RenameIndex(
                name: "ix_asp_net_users_normalized_email",
                table: "asp_net_users",
                newName: "email_index");

            migrationBuilder.RenameIndex(
                name: "ix_asp_net_roles_normalized_name",
                table: "asp_net_roles",
                newName: "role_name_index");

            migrationBuilder.AddForeignKey(
                name: "FK_goods_receipt_note_lines_delivery_note_lines_DeliveryNoteLi~",
                table: "goods_receipt_note_lines",
                columns: new[] { "DeliveryNoteLineDeliveryNoteId", "DeliveryNoteLineLineNumber" },
                principalTable: "delivery_note_lines",
                principalColumns: new[] { "delivery_note_id", "line_number" });

            migrationBuilder.AddForeignKey(
                name: "FK_return_authorization_lines_sales_order_lines_OriginalSalesO~",
                table: "return_authorization_lines",
                columns: new[] { "OriginalSalesOrderLineSalesOrderId", "OriginalSalesOrderLineLineNumber" },
                principalTable: "sales_order_lines",
                principalColumns: new[] { "sales_order_id", "line_number" });

            migrationBuilder.AddForeignKey(
                name: "FK_sales_order_lines_projects_ProjectId1",
                table: "sales_order_lines",
                column: "ProjectId1",
                principalSchema: "public",
                principalTable: "projects",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "FK_sales_order_lines_sales_order_lines_ParentSalesOrderLineSal~",
                table: "sales_order_lines",
                columns: new[] { "ParentSalesOrderLineSalesOrderId", "ParentSalesOrderLineLineNumber" },
                principalTable: "sales_order_lines",
                principalColumns: new[] { "sales_order_id", "line_number" });

            migrationBuilder.AddForeignKey(
                name: "FK_sales_orders_sales_territories_SalesTerritoryId1",
                schema: "public",
                table: "sales_orders",
                column: "SalesTerritoryId1",
                principalTable: "sales_territories",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "FK_submittal_reviews_technical_submittals_TechnicalSubmittalId1",
                table: "submittal_reviews",
                column: "TechnicalSubmittalId1",
                principalTable: "technical_submittals",
                principalColumn: "id");
        }
    }
}
