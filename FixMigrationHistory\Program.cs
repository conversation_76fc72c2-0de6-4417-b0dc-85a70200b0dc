using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Npgsql;

class Program
{
    static async Task Main(string[] args)
    {
        var connectionString = "Host=localhost;Port=5432;Database=procuretopaydb;Username=postgres;Password=localdevpassword";

        try
        {
            using var connection = new NpgsqlConnection(connectionString);
            await connection.OpenAsync();

            Console.WriteLine("✅ Connected to database successfully!");
            Console.WriteLine();

            // Execute the migration history fix step by step
            await FixMigrationHistoryTable(connection);

            Console.WriteLine();
            Console.WriteLine("🎉 Migration history table fix completed!");

        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Error: {ex.Message}");
            return;
        }
    }

    static async Task FixMigrationHistoryTable(NpgsqlConnection connection)
    {
        Console.WriteLine("=== Step 1: Check current table structure ===");

        // Check current table structure
        var checkTableQuery = @"
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns
            WHERE table_name = '__EFMigrationsHistory'
            ORDER BY ordinal_position;";

        using var cmd1 = new NpgsqlCommand(checkTableQuery, connection);
        using var reader1 = await cmd1.ExecuteReaderAsync();

        bool tableExists = false;
        Console.WriteLine("Current table structure:");
        while (await reader1.ReadAsync())
        {
            tableExists = true;
            Console.WriteLine($"  {reader1.GetString(0)} ({reader1.GetString(1)}) - Nullable: {reader1.GetString(2)}");
        }
        reader1.Close();

        if (!tableExists)
        {
            Console.WriteLine("  Table does not exist - nothing to fix");
            return;
        }

        Console.WriteLine();
        Console.WriteLine("=== Step 2: Backup existing data ===");

        // Backup existing data
        var backupData = new List<(string migrationId, string productVersion)>();

        try
        {
            // Try snake_case first
            var backupQuery = @"SELECT migration_id, product_version FROM ""__EFMigrationsHistory"";";
            using var cmd2 = new NpgsqlCommand(backupQuery, connection);
            using var reader2 = await cmd2.ExecuteReaderAsync();

            while (await reader2.ReadAsync())
            {
                backupData.Add((reader2.GetString(0), reader2.GetString(1)));
            }
            reader2.Close();
            Console.WriteLine($"✅ Backed up {backupData.Count} migration records (snake_case columns)");
        }
        catch (Exception)
        {
            try
            {
                // Try PascalCase
                var backupQuery = @"SELECT ""MigrationId"", ""ProductVersion"" FROM ""__EFMigrationsHistory"";";
                using var cmd3 = new NpgsqlCommand(backupQuery, connection);
                using var reader3 = await cmd3.ExecuteReaderAsync();

                while (await reader3.ReadAsync())
                {
                    backupData.Add((reader3.GetString(0), reader3.GetString(1)));
                }
                reader3.Close();
                Console.WriteLine($"✅ Backed up {backupData.Count} migration records (PascalCase columns)");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️  Could not backup migration history: {ex.Message}");
            }
        }

        Console.WriteLine();
        Console.WriteLine("=== Step 3: Drop and recreate table ===");

        // Drop existing table
        var dropTableQuery = @"DROP TABLE IF EXISTS ""__EFMigrationsHistory"";";
        using var cmd4 = new NpgsqlCommand(dropTableQuery, connection);
        await cmd4.ExecuteNonQueryAsync();
        Console.WriteLine("✅ Dropped existing table");

        // Create new table with correct structure
        var createTableQuery = @"
            CREATE TABLE ""__EFMigrationsHistory"" (
                ""MigrationId"" character varying(150) NOT NULL,
                ""ProductVersion"" character varying(32) NOT NULL,
                CONSTRAINT ""PK___EFMigrationsHistory"" PRIMARY KEY (""MigrationId"")
            );";
        using var cmd5 = new NpgsqlCommand(createTableQuery, connection);
        await cmd5.ExecuteNonQueryAsync();
        Console.WriteLine("✅ Created new table with PascalCase columns");

        Console.WriteLine();
        Console.WriteLine("=== Step 4: Restore data ===");

        // Restore data
        if (backupData.Count > 0)
        {
            foreach (var (migrationId, productVersion) in backupData)
            {
                var insertQuery = @"INSERT INTO ""__EFMigrationsHistory"" (""MigrationId"", ""ProductVersion"") VALUES (@migrationId, @productVersion);";
                using var cmd6 = new NpgsqlCommand(insertQuery, connection);
                cmd6.Parameters.AddWithValue("migrationId", migrationId);
                cmd6.Parameters.AddWithValue("productVersion", productVersion);
                await cmd6.ExecuteNonQueryAsync();
            }
            Console.WriteLine($"✅ Restored {backupData.Count} migration records");
        }
        else
        {
            Console.WriteLine("ℹ️  No migration history to restore");
        }

        Console.WriteLine();
        Console.WriteLine("=== Step 5: Verify final structure ===");

        // Verify final structure
        using var cmd7 = new NpgsqlCommand(checkTableQuery, connection);
        using var reader7 = await cmd7.ExecuteReaderAsync();

        Console.WriteLine("Final table structure:");
        while (await reader7.ReadAsync())
        {
            Console.WriteLine($"  {reader7.GetString(0)} ({reader7.GetString(1)}) - Nullable: {reader7.GetString(2)}");
        }
        reader7.Close();

        Console.WriteLine();
        Console.WriteLine("=== Step 6: Show migration history ===");

        // Show current migration history
        var historyQuery = @"SELECT ""MigrationId"", ""ProductVersion"" FROM ""__EFMigrationsHistory"" ORDER BY ""MigrationId"";";
        using var cmd8 = new NpgsqlCommand(historyQuery, connection);
        using var reader8 = await cmd8.ExecuteReaderAsync();

        if (reader8.HasRows)
        {
            Console.WriteLine("Current migration history:");
            while (await reader8.ReadAsync())
            {
                Console.WriteLine($"  {reader8.GetString(0)} (EF {reader8.GetString(1)})");
            }
        }
        else
        {
            Console.WriteLine("No migration history found");
        }
        reader8.Close();
    }
}
