using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using ProcureToPay.Infrastructure.Persistence;
using ProcureToPay.Infrastructure.Services;

class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("=== EF Core Entity Discovery Debug Tool ===");
        Console.WriteLine();

        try
        {
            // Create a minimal service collection
            var services = new ServiceCollection();
            
            // Add logging
            services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Debug));
            
            // Add tenant provider
            services.AddScoped<ITenantProvider, DesignTimeTenantProvider>();
            
            // Add naming convention options
            services.Configure<NamingConventionOptions>(options =>
            {
                options.EnableLogging = true;
                options.EnableSnakeCaseConversion = true;
                options.ConvertTableNames = true;
                options.ConvertColumnNames = true;
                options.EnableValidation = true;
            });
            
            // Add DbContext
            services.AddDbContext<ApplicationDbContext>(options =>
            {
                options.UseNpgsql("Host=localhost;Port=5432;Database=procuretopaydb;Username=postgres;Password=localdevpassword");
                options.EnableSensitiveDataLogging();
                options.EnableDetailedErrors();
            });
            
            var serviceProvider = services.BuildServiceProvider();
            
            using var scope = serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();
            
            Console.WriteLine("✅ ApplicationDbContext created successfully");
            Console.WriteLine();
            
            // Get the model
            var model = context.Model;
            var entityTypes = model.GetEntityTypes().ToList();
            
            Console.WriteLine($"📊 Total entity types discovered: {entityTypes.Count}");
            Console.WriteLine();
            
            if (entityTypes.Count == 0)
            {
                Console.WriteLine("❌ No entity types discovered! This explains why migrations are empty.");
                Console.WriteLine();
                Console.WriteLine("Possible causes:");
                Console.WriteLine("1. Entity configurations are not being found");
                Console.WriteLine("2. DbSet properties are not being recognized");
                Console.WriteLine("3. Assembly scanning is not working");
                Console.WriteLine("4. Compilation issues with entity classes");
            }
            else
            {
                Console.WriteLine("✅ Entity types discovered:");
                foreach (var entityType in entityTypes.OrderBy(e => e.Name))
                {
                    var tableName = entityType.GetTableName();
                    var schema = entityType.GetSchema();
                    var clrType = entityType.ClrType.Name;
                    
                    Console.WriteLine($"  • {clrType} -> {schema}.{tableName}");
                    
                    // Show some properties
                    var properties = entityType.GetProperties().Take(3);
                    foreach (var prop in properties)
                    {
                        var columnName = prop.GetColumnName();
                        Console.WriteLine($"    - {prop.Name} -> {columnName}");
                    }
                    
                    if (entityType.GetProperties().Count() > 3)
                    {
                        Console.WriteLine($"    ... and {entityType.GetProperties().Count() - 3} more properties");
                    }
                    Console.WriteLine();
                }
            }
            
            // Test if we can generate a migration script
            Console.WriteLine("=== Testing Migration Script Generation ===");
            try
            {
                var script = context.Database.GenerateCreateScript();
                if (string.IsNullOrWhiteSpace(script))
                {
                    Console.WriteLine("❌ Generated script is empty");
                }
                else
                {
                    Console.WriteLine($"✅ Generated script length: {script.Length} characters");
                    Console.WriteLine();
                    Console.WriteLine("First 500 characters of script:");
                    Console.WriteLine(script.Substring(0, Math.Min(500, script.Length)));
                    if (script.Length > 500)
                    {
                        Console.WriteLine("...");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error generating script: {ex.Message}");
            }
            
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Error: {ex.Message}");
            if (ex.InnerException != null)
            {
                Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
            }
            Console.WriteLine();
            Console.WriteLine("Stack trace:");
            Console.WriteLine(ex.StackTrace);
        }
    }
}
