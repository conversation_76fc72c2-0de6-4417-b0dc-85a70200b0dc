# Comprehensive EF Core Migration Fix Script
# This script fixes the snake_case naming convention issue and creates clean migrations

param(
    [Parameter(Mandatory=$false)]
    [string]$MigrationName = "InitialCleanSchema",
    
    [Parameter(Mandatory=$false)]
    [string]$InfrastructureProject = "ProcureToPay.Infrastructure",
    
    [Parameter(Mandatory=$false)]
    [string]$WebAppProject = "ProcureToPay.WebApp\ProcureToPay.WebApp",
    
    [Parameter(Mandatory=$false)]
    [string]$MigrationsOutputDir = "PersistenceMigrations",
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipDatabaseDrop = $false
)

# Color definitions for output
$colorInfo = "Cyan"
$colorSuccess = "Green"
$colorWarning = "Yellow"
$colorError = "Red"

Write-Host "=== Comprehensive EF Core Migration Fix ===" -ForegroundColor $colorInfo
Write-Host "This script will fix the snake_case naming convention issue and create clean migrations" -ForegroundColor $colorInfo
Write-Host ""

# Step 1: Verify Aspire is running
Write-Host "Step 1: Checking if .NET Aspire is running..." -ForegroundColor $colorInfo
$aspireProcesses = Get-Process -Name "ProcureToPaySolution.AppHost" -ErrorAction SilentlyContinue
if ($aspireProcesses) {
    Write-Host "  ✅ .NET Aspire is running" -ForegroundColor $colorSuccess
} else {
    Write-Host "  ⚠️  .NET Aspire is not running. Please start it first:" -ForegroundColor $colorWarning
    Write-Host "     dotnet run --project ProcureToPaySolution.AppHost" -ForegroundColor $colorWarning
    Write-Host ""
    $response = Read-Host "Do you want to continue anyway? (y/N)"
    if ($response -ne "y" -and $response -ne "Y") {
        Write-Host "Exiting. Please start Aspire first." -ForegroundColor $colorError
        exit 1
    }
}

# Step 2: Drop and recreate database (if not skipped)
if (-not $SkipDatabaseDrop) {
    Write-Host ""
    Write-Host "Step 2: Dropping and recreating database..." -ForegroundColor $colorInfo
    
    try {
        $dropCmd = "dotnet ef database drop --project $InfrastructureProject --startup-project $WebAppProject --context ApplicationDbContext --force"
        Write-Host "  Running: $dropCmd" -ForegroundColor $colorInfo
        Invoke-Expression $dropCmd
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✅ Database dropped successfully" -ForegroundColor $colorSuccess
        } else {
            Write-Host "  ⚠️  Database drop failed or database didn't exist" -ForegroundColor $colorWarning
        }
    } catch {
        Write-Host "  ⚠️  Error dropping database: $_" -ForegroundColor $colorWarning
    }
} else {
    Write-Host ""
    Write-Host "Step 2: Skipping database drop (as requested)" -ForegroundColor $colorWarning
}

# Step 3: Remove any existing migration files
Write-Host ""
Write-Host "Step 3: Cleaning up existing migration files..." -ForegroundColor $colorInfo

$migrationsFolder = Join-Path $InfrastructureProject $MigrationsOutputDir
if (Test-Path $migrationsFolder) {
    $migrationFiles = Get-ChildItem -Path $migrationsFolder -Filter "*.cs" | Where-Object { $_.Name -notlike "*ModelSnapshot.cs" }
    if ($migrationFiles) {
        Write-Host "  Found $($migrationFiles.Count) migration files to remove" -ForegroundColor $colorInfo
        foreach ($file in $migrationFiles) {
            Remove-Item $file.FullName -Force
            Write-Host "    Removed: $($file.Name)" -ForegroundColor $colorInfo
        }
    } else {
        Write-Host "  No migration files found to remove" -ForegroundColor $colorInfo
    }
} else {
    Write-Host "  Migrations folder does not exist: $migrationsFolder" -ForegroundColor $colorInfo
}

# Step 4: Create fresh migration
Write-Host ""
Write-Host "Step 4: Creating fresh migration..." -ForegroundColor $colorInfo

try {
    $migrationCmd = "dotnet ef migrations add $MigrationName --project $InfrastructureProject --startup-project $WebAppProject --context ApplicationDbContext --output-dir $MigrationsOutputDir"
    Write-Host "  Running: $migrationCmd" -ForegroundColor $colorInfo
    
    Invoke-Expression $migrationCmd
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "  ✅ Migration '$MigrationName' created successfully!" -ForegroundColor $colorSuccess
    } else {
        Write-Error "  ❌ Failed to create migration. See output above for details."
        exit 1
    }
} catch {
    Write-Error "  ❌ An error occurred while creating the migration: $_"
    exit 1
}

# Step 5: Apply migration to database
Write-Host ""
Write-Host "Step 5: Applying migration to database..." -ForegroundColor $colorInfo

try {
    $updateCmd = "dotnet ef database update --project $InfrastructureProject --startup-project $WebAppProject --context ApplicationDbContext"
    Write-Host "  Running: $updateCmd" -ForegroundColor $colorInfo
    
    Invoke-Expression $updateCmd
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "  ✅ Database updated successfully!" -ForegroundColor $colorSuccess
    } else {
        Write-Error "  ❌ Failed to update database. See output above for details."
        exit 1
    }
} catch {
    Write-Error "  ❌ An error occurred while updating the database: $_"
    exit 1
}

# Step 6: Generate SQL script for reference
Write-Host ""
Write-Host "Step 6: Generating SQL script for reference..." -ForegroundColor $colorInfo

try {
    $sqlScriptPath = "migration_script_$(Get-Date -Format 'yyyyMMdd_HHmmss').sql"
    $scriptCmd = "dotnet ef migrations script --project $InfrastructureProject --startup-project $WebAppProject --output $sqlScriptPath --idempotent"
    Write-Host "  Running: $scriptCmd" -ForegroundColor $colorInfo
    
    Invoke-Expression $scriptCmd
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "  ✅ SQL script generated: $sqlScriptPath" -ForegroundColor $colorSuccess
    } else {
        Write-Host "  ⚠️  Failed to generate SQL script" -ForegroundColor $colorWarning
    }
} catch {
    Write-Host "  ⚠️  Error generating SQL script: $_" -ForegroundColor $colorWarning
}

# Step 7: Verification
Write-Host ""
Write-Host "Step 7: Verification..." -ForegroundColor $colorInfo

try {
    # Check migration history
    Write-Host "  Checking migration history..." -ForegroundColor $colorInfo
    $historyCmd = "dotnet ef migrations list --project $InfrastructureProject --startup-project $WebAppProject --context ApplicationDbContext"
    Invoke-Expression $historyCmd
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "  ✅ Migration history verified" -ForegroundColor $colorSuccess
    } else {
        Write-Host "  ⚠️  Could not verify migration history" -ForegroundColor $colorWarning
    }
} catch {
    Write-Host "  ⚠️  Error during verification: $_" -ForegroundColor $colorWarning
}

Write-Host ""
Write-Host "=== Migration Fix Complete ===" -ForegroundColor $colorSuccess
Write-Host "✅ Database has been recreated with proper snake_case naming convention" -ForegroundColor $colorSuccess
Write-Host "✅ Migration history table uses correct PascalCase column names" -ForegroundColor $colorSuccess
Write-Host "✅ Fresh migration created and applied" -ForegroundColor $colorSuccess
Write-Host ""
Write-Host "Next steps:" -ForegroundColor $colorInfo
Write-Host "1. Test your application to ensure it works correctly" -ForegroundColor $colorInfo
Write-Host "2. Verify that snake_case naming is applied to your domain tables" -ForegroundColor $colorInfo
Write-Host "3. Check that the __EFMigrationsHistory table uses PascalCase columns" -ForegroundColor $colorInfo
