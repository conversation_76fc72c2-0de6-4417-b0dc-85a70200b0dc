-- Fix EF Core Migration History Table Naming Convention
-- This script fixes the column naming mismatch in __EFMigrationsHistory table

-- Step 1: Check current table structure
SELECT 
    column_name, 
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name = '__EFMigrationsHistory' 
ORDER BY ordinal_position;

-- Step 2: Backup existing data (if any)
CREATE TEMP TABLE migration_backup AS 
SELECT * FROM "__EFMigrationsHistory";

-- Step 3: Drop the existing table
DROP TABLE IF EXISTS "__EFMigrationsHistory";

-- Step 4: Recreate table with correct PascalCase column names
CREATE TABLE "__EFMigrationsHistory" (
    "MigrationId" character varying(150) NOT NULL,
    "ProductVersion" character varying(32) NOT NULL,
    CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY ("MigrationId")
);

-- Step 5: Restore data if it existed (handling both naming conventions)
DO $$
DECLARE
    backup_count INTEGER;
    has_snake_case BOOLEAN := FALSE;
    has_pascal_case BOOLEAN := FALSE;
BEGIN
    -- Check if backup has data
    SELECT COUNT(*) INTO backup_count FROM migration_backup;
    
    IF backup_count > 0 THEN
        -- Check which column naming convention the backup uses
        BEGIN
            PERFORM migration_id FROM migration_backup LIMIT 1;
            has_snake_case := TRUE;
        EXCEPTION WHEN OTHERS THEN
            has_snake_case := FALSE;
        END;
        
        BEGIN
            PERFORM "MigrationId" FROM migration_backup LIMIT 1;
            has_pascal_case := TRUE;
        EXCEPTION WHEN OTHERS THEN
            has_pascal_case := FALSE;
        END;
        
        -- Restore data based on detected naming convention
        IF has_snake_case THEN
            INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
            SELECT migration_id, product_version FROM migration_backup;
            RAISE NOTICE 'Restored % migration records from snake_case columns', backup_count;
        ELSIF has_pascal_case THEN
            INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
            SELECT "MigrationId", "ProductVersion" FROM migration_backup;
            RAISE NOTICE 'Restored % migration records from PascalCase columns', backup_count;
        ELSE
            RAISE NOTICE 'Could not determine column naming convention in backup';
        END IF;
    ELSE
        RAISE NOTICE 'No existing migration history to restore';
    END IF;
END $$;

-- Step 6: Verify final structure
SELECT 
    column_name, 
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name = '__EFMigrationsHistory' 
ORDER BY ordinal_position;

-- Step 7: Show current migration history
SELECT "MigrationId", "ProductVersion" FROM "__EFMigrationsHistory" ORDER BY "MigrationId";
